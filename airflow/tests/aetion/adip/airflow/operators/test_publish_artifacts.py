import pytest

from dags.operators.artifacts_publisher import ArtifactsPublisher
from dags.tools.config import databricks_libraries_from_artifacts

from tests.spectools import Mock<PERSON>


def test_check_revision():
    publisher = ArtifactsPublisher(repo="repo",
                                   private_key="private_key",
                                   git_default_branch="git_default_branch",
                                   branch="master",
                                   global_artifacts_path="global_artifacts_path",
                                   artifacts={
                                       "adip_utilities-0.0.1-py3-none-any.whl": True,
                                       "adip_utilities-0.0.1-py3-none-any.egg": True,
                                       "automated_ingestion_config-0.0.0-py3-none-any.whl": True,
                                       "automated_ingestion_config-0.0.0-py3-none-any.egg": True,
                                       "coding_systems.csv": False,
                                       "generic_runner.py": False,
                                       "raw-data-catalog.yml": False,
                                       "spark-connector-assembly-1.3-SNAPSHOT.jar": True,
                                       "spark_validator-1.0b0-py3-none-any.whl": True,
                                       "spark_validator-1.0b0-py3-none-any.egg": True,
                                       "unarchiver-0.0.1-py3-none-any.whl": True,
                                       "unarchiver-0.0.1-py3-none-any.egg": True,
                                       "unarchiver_cluster_init.sh": False
                                   },
                                   dataset_artifacts_path="s3://demo.aetion.com/etl/marketscan/20210901/artifacts/",
                                   rdc="raw-data-catalog.yml",
                                   revision="20210901",
                                   task_id="deploy_package",
                                   )

    # success if does not fail
    publisher.check_revision()

def test_skip_ndc_artifacts():
    publisher = ArtifactsPublisher(
        task_id='publish_artifacts',
        global_artifacts_path="global_artifacts_path",
        dataset_artifacts_path="s3://demo.aetion.com/etl/marketscan/20210901_v2/artifacts/",
        artifacts={
            "adip_utilities-0.0.1-py3-none-any.whl": True,
            "adip_utilities-0.0.1-py3-none-any.egg": True,
            "automated_ingestion_config-0.0.0-py3-none-any.whl": True,
            "automated_ingestion_config-0.0.0-py3-none-any.egg": True,
            "coding_systems.csv": False,
            "generic_runner.py": False,
            "raw-data-catalog.yml": False,
            "spark-connector-assembly-1.3-SNAPSHOT.jar": True,
            "spark_validator-1.0b0-py3-none-any.whl": True,
            "spark_validator-1.0b0-py3-none-any.egg": True,
            "unarchiver-0.0.1-py3-none-any.whl": True,
            "unarchiver-0.0.1-py3-none-any.egg": True,
            "unarchiver_cluster_init.sh": False
        },
        repo="repo",
        private_key="private_key",
        branch="master",
        rdc="raw-data-catalog.yml",
        git_default_branch="git_default_branch"
    )

    # success if does not fail
    publisher.download_ndc_lookup_file()

def test_check_revision_fail():
    publisher = ArtifactsPublisher(repo="repo",
                                   private_key="private_key",
                                   git_default_branch="git_default_branch",
                                   branch="master",
                                   global_artifacts_path="global_artifacts_path",
                                   artifacts={
                                       "adip_utilities-0.0.1-py3-none-any.whl": True,
                                       "adip_utilities-0.0.1-py3-none-any.egg": True,
                                       "automated_ingestion_config-0.0.0-py3-none-any.whl": True,
                                       "automated_ingestion_config-0.0.0-py3-none-any.egg": True,
                                       "coding_systems.csv": False,
                                       "generic_runner.py": False,
                                       "raw-data-catalog.yml": False,
                                       "spark-connector-assembly-1.3-SNAPSHOT.jar": True,
                                       "spark_validator-1.0b0-py3-none-any.whl": True,
                                       "spark_validator-1.0b0-py3-none-any.egg": True,
                                       "unarchiver-0.0.1-py3-none-any.whl": True,
                                       "unarchiver-0.0.1-py3-none-any.egg": True,
                                       "unarchiver_cluster_init.sh": False
                                   },
                                   dataset_artifacts_path="s3://demo.aetion.com/etl/marketscan/20210901_v2/artifacts/",
                                   rdc="raw-data-catalog.yml",
                                   revision="20210901_v2",
                                   task_id="deploy_package",
                                   )

    with pytest.raises(Exception) as e_info:
        publisher.check_revision()


def test_databricks_library_format():
    artifacts = {
        "adip_utilities-0.0.1-py3-none-any.whl": True,
        "adip_utilities-0.0.1-py3-none-any.egg": True,
        "automated_ingestion_config-0.0.0-py3-none-any.whl": True,
        "automated_ingestion_config-0.0.0-py3-none-any.egg": True,
        "coding_systems.csv": False,
        "generic_runner.py": False,
        "raw-data-catalog.yml": False,
        "spark-connector-assembly-1.3-SNAPSHOT.jar": True,
        "spark_validator-1.0b0-py3-none-any.whl": True,
        "spark_validator-1.0b0-py3-none-any.egg": True,
        "unarchiver-0.0.1-py3-none-any.whl": True,
        "unarchiver-0.0.1-py3-none-any.egg": True,
        "unarchiver_cluster_init.sh": False
    }
    libraries = [
        {"whl": "adip_utilities-0.0.1-py3-none-any.whl"},
        {"egg": "adip_utilities-0.0.1-py3-none-any.egg"},
        {'whl': 'automated_ingestion_config-0.0.0-py3-none-any.whl'},
        {'egg': 'automated_ingestion_config-0.0.0-py3-none-any.egg'},
        {"jar": "spark-connector-assembly-1.3-SNAPSHOT.jar"},
        {"whl": "spark_validator-1.0b0-py3-none-any.whl"},
        {"egg": "spark_validator-1.0b0-py3-none-any.egg"},
        {"whl": "unarchiver-0.0.1-py3-none-any.whl"},
        {"egg": "unarchiver-0.0.1-py3-none-any.egg"}
    ]
    databricks_libraries = databricks_libraries_from_artifacts(artifacts)
    assert databricks_libraries == libraries


def test_load_ndc_mapping_file_name():
    coding_system_path = (
        ArtifactsPublisher.load_ndc_mapping_file_name(
            'tests/resources/coding_systems.csv',
            MockFS()))

    assert coding_system_path == 'versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz'


def test_load_ndc_mapping_file_name_with_missing_mapping():
    with pytest.raises(Exception) as ex:
        ArtifactsPublisher.load_ndc_mapping_file_name(
            'tests/resources/coding_systems_missing_mapping.csv',
            MockFS())

        assert str(ex) == "coding system for NDC_FDB_2014 not found"
