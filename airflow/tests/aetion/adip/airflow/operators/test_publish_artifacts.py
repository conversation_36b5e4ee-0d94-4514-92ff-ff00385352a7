import os
import tempfile
import zipfile
from collections import namedtuple
from unittest.mock import Mock, patch, MagicMock, mock_open
from typing import Any

import pytest
from airflow.exceptions import AirflowException
from aetion.adip.airflow.integrations.s3.s3_support import AetionS3FileSystem

from dags.operators.artifacts_publisher import ArtifactsPublisher
from dags.tools.config import databricks_libraries_from_artifacts
from hooks.git_hook import GitHook
from tests.spectools import MockFS


@pytest.fixture
def sample_artifacts() -> dict[str, bool]:
    """
    Create a standard artifacts dictionary for testing artifact publishing scenarios.
    """
    return {
        "adip_utilities-0.0.1-py3-none-any.whl": True,
        "adip_utilities-0.0.1-py3-none-any.egg": True,
        "automated_ingestion_config-0.0.0-py3-none-any.whl": True,
        "automated_ingestion_config-0.0.0-py3-none-any.egg": True,
        "coding_systems.csv": False,
        "generic_runner.py": False,
        "raw-data-catalog.yml": False,
        "spark-connector-assembly-1.3-SNAPSHOT.jar": True,
        "spark_validator-1.0b0-py3-none-any.whl": True,
        "spark_validator-1.0b0-py3-none-any.egg": True,
        "unarchiver-0.0.1-py3-none-any.whl": True,
        "unarchiver-0.0.1-py3-none-any.egg": True,
        "unarchiver_cluster_init.sh": False
    }


@pytest.fixture
def mock_filesystem():
    """
    Create a mock AetionS3FileSystem for isolated testing of operator logic.
    """
    fs = MagicMock(spec=AetionS3FileSystem)
    fs.copy_files_parallel = Mock()
    fs.copy_with_pattern_parallel = Mock()
    fs.download_files_parallel = Mock()
    fs.put = Mock()
    fs.exists = Mock(return_value=True)

    # Mock the open method to return a context manager
    mock_file = MagicMock()
    mock_file.__enter__.return_value = mock_file
    mock_file.__exit__.return_value = None
    mock_file.read.return_value = "NDC_FDB_2014,versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz"
    fs.open.return_value = mock_file

    return fs


@pytest.fixture
def mock_git_hook():
    """
    Create a mock GitHook for isolated testing of git operations.
    """
    git_hook = MagicMock(spec=GitHook)
    git_hook.__enter__.return_value = git_hook
    git_hook.__exit__.return_value = None
    git_hook.clone = Mock()
    return git_hook


@pytest.fixture
def artifacts_publisher(sample_artifacts: dict[str, bool]):
    """
    Create a configured ArtifactsPublisher operator for testing.
    """
    return ArtifactsPublisher(
        repo="test-repo",
        private_key="test-private-key",
        git_default_branch="main",
        branch="feature-branch",
        global_artifacts_path="s3://test-bucket/global/artifacts",
        dataset_artifacts_path="s3://test-bucket/dataset/artifacts",
        rdc="raw-data-catalog.yml",
        artifacts=sample_artifacts,
        revision="20210901",
        task_id="test_publish_artifacts",
    )


class TestArtifactsPublisher:
    """Test suite for ArtifactsPublisher operator."""

    def test_init_with_all_parameters(self, sample_artifacts: dict[str, bool]):
        """Test initialization with all parameters."""

        # Given: All possible initialization parameters for artifact publishing
        # including custom S3 paths, git configuration, and AWS connection

        # When: The ArtifactsPublisher operator is instantiated with all parameters
        operator = ArtifactsPublisher(
            repo="test-repo",
            private_key="test-private-key",
            git_default_branch="main",
            branch="feature-branch",
            global_artifacts_path="s3://test-bucket/global",
            dataset_artifacts_path="s3://test-bucket/dataset",
            rdc="raw-data-catalog.yml",
            artifacts=sample_artifacts,
            aws_conn_id="custom_aws_conn",
            git_meta_repo="test-meta-repo",
            client="test-client",
            dataset="test-dataset",
            revision="20210901",
            git_config_file_path="/config/path",
            config_file_url="s3://config/url",
            task_id="test_task",
        )

        # Then: All parameters should be properly assigned to instance variables
        assert operator.repo == "test-repo"
        assert operator.private_key == "test-private-key"
        assert operator.git_default_branch == "main"
        assert operator.branch == "feature-branch"
        assert operator.global_artifacts_path == "s3://test-bucket/global"
        assert operator.dataset_artifacts_path == "s3://test-bucket/dataset"
        assert operator.rdc == "raw-data-catalog.yml"
        assert operator.artifacts == sample_artifacts
        assert operator.aws_conn_id == "custom_aws_conn"
        assert operator.git_meta_repo == "test-meta-repo"
        assert operator.client == "test-client"
        assert operator.dataset == "test-dataset"
        assert operator.revision == "20210901"
        assert operator.git_config_file_path == "/config/path"
        assert operator.config_file_url == "s3://config/url"


    def test_check_revision_success(self, sample_artifacts: dict[str, bool]):
        """Test successful revision validation with numeric revision."""

        # Given: An ArtifactsPublisher with a valid numeric revision
        # representing a standard dataset processing scenario
        publisher = ArtifactsPublisher(
            repo="test-repo",
            private_key="test-private-key",
            git_default_branch="main",
            branch="master",
            global_artifacts_path="s3://test-bucket/global",
            artifacts=sample_artifacts,
            dataset_artifacts_path="s3://demo.aetion.com/etl/marketscan/20210901/artifacts/",
            rdc="raw-data-catalog.yml",
            revision="20210901",
            task_id="test_check_revision",
        )

        # When: The check_revision method is called with a valid numeric revision
        # Then: The method should complete successfully without raising exceptions
        publisher.check_revision()  # Should not raise any exception

    @patch('dags.operators.artifacts_publisher.GitHook')
    @patch('dags.operators.artifacts_publisher.tempfile.TemporaryDirectory')
    @patch('os.path.exists')
    def test_download_ndc_lookup_file_success(
        self,
        mock_exists: Mock,
        mock_temp_dir: Mock,
        mock_git_hook_class: Mock,
        sample_artifacts: dict[str, bool],
        mock_filesystem: Mock
    ):
        """Test successful NDC lookup file download from git repository."""

        # Given: An ArtifactsPublisher configured for NDC file download
        # with mocked git operations and filesystem
        publisher = ArtifactsPublisher(
            task_id='test_ndc_download',
            global_artifacts_path="s3://test-bucket/global",
            dataset_artifacts_path="s3://demo.aetion.com/etl/marketscan/20210901_v2/artifacts/",
            artifacts=sample_artifacts,
            repo="test-repo",
            private_key="test-private-key",
            branch="master",
            rdc="raw-data-catalog.yml",
            git_default_branch="main",
            git_meta_repo="test-meta-repo"
        )

        # Mock the filesystem property
        publisher._fs = mock_filesystem

        # Mock temporary directory context manager
        temp_dir_mock = MagicMock()
        temp_dir_mock.__enter__.return_value = "/tmp/test"
        temp_dir_mock.__exit__.return_value = None
        mock_temp_dir.return_value = temp_dir_mock

        # Mock git hook context manager
        mock_git_hook = MagicMock()
        mock_git_hook.__enter__.return_value = mock_git_hook
        mock_git_hook.__exit__.return_value = None
        mock_git_hook.clone = Mock()
        mock_git_hook_class.return_value = mock_git_hook

        # Mock directory existence
        mock_exists.return_value = True

        # When: The download_ndc_lookup_file method is called
        # Then: The method should complete successfully without exceptions
        publisher.download_ndc_lookup_file()  # Should not raise any exception

    def test_check_revision_failure(self, sample_artifacts: dict[str, bool]):
        """Test revision validation failure with non-numeric revision."""

        # Given: An ArtifactsPublisher with an invalid non-numeric revision
        # containing underscores which should trigger validation failure
        publisher = ArtifactsPublisher(
            repo="test-repo",
            private_key="test-private-key",
            git_default_branch="main",
            branch="master",
            global_artifacts_path="s3://test-bucket/global",
            artifacts=sample_artifacts,
            dataset_artifacts_path="s3://demo.aetion.com/etl/marketscan/20210901_v2/artifacts/",
            rdc="raw-data-catalog.yml",
            revision="20210901_v2",  # Invalid: contains underscore
            task_id="test_check_revision_fail",
        )

        # When: The check_revision method is called with invalid revision format
        # Then: An exception should be raised due to non-numeric revision
        with pytest.raises(Exception):
            publisher.check_revision()


    def test_databricks_library_format(self, sample_artifacts: dict[str, bool]):
        """Test conversion of artifacts to Databricks library format."""

        # Given: A standard artifacts dictionary with various file types
        # including wheel, egg, and jar files for Databricks cluster configuration

        # When: The databricks_libraries_from_artifacts function processes the artifacts
        # filtering only artifacts marked as True (installable libraries)
        databricks_libraries = databricks_libraries_from_artifacts(sample_artifacts)

        # Then: The result should contain properly formatted library specifications
        # with correct file type mappings for Databricks cluster installation
        expected_libraries = [
            {"whl": "adip_utilities-0.0.1-py3-none-any.whl"},
            {"egg": "adip_utilities-0.0.1-py3-none-any.egg"},
            {'whl': 'automated_ingestion_config-0.0.0-py3-none-any.whl'},
            {'egg': 'automated_ingestion_config-0.0.0-py3-none-any.egg'},
            {"jar": "spark-connector-assembly-1.3-SNAPSHOT.jar"},
            {"whl": "spark_validator-1.0b0-py3-none-any.whl"},
            {"egg": "spark_validator-1.0b0-py3-none-any.egg"},
            {"whl": "unarchiver-0.0.1-py3-none-any.whl"},
            {"egg": "unarchiver-0.0.1-py3-none-any.egg"}
        ]
        assert databricks_libraries == expected_libraries


    def test_load_ndc_mapping_file_name_success(self):
        """Test successful loading of NDC mapping file name from coding systems."""

        # Given: A valid coding systems CSV file with NDC_FDB_2014 mapping
        # and a mock filesystem for file operations

        # When: The load_ndc_mapping_file_name method processes the coding systems file
        # to extract the NDC mapping file path
        coding_system_path = ArtifactsPublisher.load_ndc_mapping_file_name(
            'tests/resources/coding_systems.csv',
            MockFS()
        )

        # Then: The method should return the correct versioned NDC file path
        # following the expected naming convention for NDC FDB mappings
        assert coding_system_path == 'versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz'

    def test_load_ndc_mapping_file_name_missing_mapping(self):
        """Test error handling when NDC mapping is missing from coding systems."""

        # Given: A coding systems CSV file that lacks the required NDC_FDB_2014 mapping
        # simulating a configuration error or incomplete data scenario

        # When: The load_ndc_mapping_file_name method attempts to find NDC mapping
        # Then: An exception should be raised indicating the missing coding system
        with pytest.raises(Exception) as ex:
            ArtifactsPublisher.load_ndc_mapping_file_name(
                'tests/resources/coding_systems_missing_mapping.csv',
                MockFS()
            )

        # Verify the exception message indicates the specific missing coding system
        assert str(ex.value) == "coding system for NDC_FDB_2014 not found"

    @patch('dags.operators.artifacts_publisher.tempfile.TemporaryDirectory')
    @patch('dags.operators.artifacts_publisher.zipfile.is_zipfile')
    @patch('dags.operators.artifacts_publisher.zipfile.ZipFile')
    def test_execute_success_without_revision(
        self,
        mock_zipfile: Mock,
        mock_is_zipfile: Mock,
        mock_temp_dir: Mock,
        artifacts_publisher: ArtifactsPublisher,
        mock_filesystem: Mock
    ):
        """Test successful execution of artifact publishing without revision check."""

        # Given: An ArtifactsPublisher configured without revision validation
        # and mocked filesystem operations for artifact copying
        artifacts_publisher.revision = None  # No revision to check
        artifacts_publisher._fs = mock_filesystem

        # Mock temporary directory context manager
        temp_dir_mock = MagicMock()
        temp_dir_mock.__enter__.return_value = "/tmp/test"
        temp_dir_mock.__exit__.return_value = None
        mock_temp_dir.return_value = temp_dir_mock

        # Mock file operations
        mock_is_zipfile.return_value = False  # Simulate non-zip files

        # When: The execute method is called to publish artifacts
        # Then: All file operations should complete successfully
        artifacts_publisher.execute({})

        # Verify that the main file copying operations were called
        mock_filesystem.copy_files_parallel.assert_called_once()
        mock_filesystem.copy_with_pattern_parallel.assert_called_once()
        mock_filesystem.download_files_parallel.assert_called_once()

    @patch('dags.operators.artifacts_publisher.tempfile.TemporaryDirectory')
    @patch('dags.operators.artifacts_publisher.zipfile.is_zipfile')
    def test_execute_with_revision_check(
        self,
        mock_is_zipfile: Mock,
        mock_temp_dir: Mock,
        artifacts_publisher: ArtifactsPublisher,
        mock_filesystem: Mock
    ):
        """Test execution with revision validation enabled."""

        # Given: An ArtifactsPublisher with a valid numeric revision
        # requiring revision validation before artifact processing
        artifacts_publisher._fs = mock_filesystem

        # Mock temporary directory context manager
        temp_dir_mock = MagicMock()
        temp_dir_mock.__enter__.return_value = "/tmp/test"
        temp_dir_mock.__exit__.return_value = None
        mock_temp_dir.return_value = temp_dir_mock

        # Mock file operations
        mock_is_zipfile.return_value = False

        # When: The execute method is called with revision validation
        # Then: Revision check should pass and file operations should proceed
        artifacts_publisher.execute({})

        # Verify that file operations were performed after successful revision check
        mock_filesystem.copy_files_parallel.assert_called_once()
        mock_filesystem.download_files_parallel.assert_called_once()

    @patch('dags.operators.artifacts_publisher.GitHook')
    @patch('dags.operators.artifacts_publisher.tempfile.TemporaryDirectory')
    @patch('os.path.exists')
    def test_copy_artifacts_from_git_to_s3_success(
        self,
        mock_exists: Mock,
        mock_temp_dir: Mock,
        mock_git_hook_class: Mock,
        artifacts_publisher: ArtifactsPublisher,
        mock_filesystem: Mock
    ):
        """Test successful copying of artifacts from git repository to S3."""

        # Given: An ArtifactsPublisher with git configuration and artifact copy info
        # simulating the transfer of configuration files from git to S3 storage
        artifacts_publisher._fs = mock_filesystem

        # Mock temporary directory context manager
        temp_dir_mock = MagicMock()
        temp_dir_mock.__enter__.return_value = "/tmp/git"
        temp_dir_mock.__exit__.return_value = None
        mock_temp_dir.return_value = temp_dir_mock

        # Mock git hook context manager
        mock_git_hook = MagicMock()
        mock_git_hook.__enter__.return_value = mock_git_hook
        mock_git_hook.__exit__.return_value = None
        mock_git_hook.clone = Mock()
        mock_git_hook_class.return_value = mock_git_hook
        mock_exists.return_value = True  # Artifact directory exists

        artifact_copy_info = [
            ("config.yaml", "config/path", "s3://bucket/config.yaml")
        ]

        # When: The copy_artifacts_from_git_to_s3 method is called
        # Then: Git clone and S3 upload operations should complete successfully
        artifacts_publisher.copy_artifacts_from_git_to_s3(artifact_copy_info)

        # Verify git operations and S3 upload were performed
        mock_git_hook.clone.assert_called_once()
        mock_filesystem.put.assert_called_once()

    @patch('dags.operators.artifacts_publisher.GitHook')
    @patch('dags.operators.artifacts_publisher.tempfile.TemporaryDirectory')
    @patch('os.path.exists')
    def test_copy_artifacts_from_git_to_s3_missing_artifact(
        self,
        mock_exists: Mock,
        mock_temp_dir: Mock,
        mock_git_hook_class: Mock,
        artifacts_publisher: ArtifactsPublisher,
        mock_filesystem: Mock
    ):
        """Test error handling when artifact directory is missing in git repository."""

        # Given: An ArtifactsPublisher with git configuration but missing artifact directory
        # simulating a scenario where expected files are not present in the repository
        artifacts_publisher._fs = mock_filesystem

        # Mock temporary directory context manager
        temp_dir_mock = MagicMock()
        temp_dir_mock.__enter__.return_value = "/tmp/git"
        temp_dir_mock.__exit__.return_value = None
        mock_temp_dir.return_value = temp_dir_mock

        # Mock git hook context manager
        mock_git_hook = MagicMock()
        mock_git_hook.__enter__.return_value = mock_git_hook
        mock_git_hook.__exit__.return_value = None
        mock_git_hook.clone = Mock()
        mock_git_hook_class.return_value = mock_git_hook
        mock_exists.return_value = False  # Artifact directory does not exist

        artifact_copy_info = [
            ("missing_config.yaml", "missing/path", "s3://bucket/config.yaml")
        ]

        # When: The copy_artifacts_from_git_to_s3 method is called with missing artifact
        # Then: An AirflowException should be raised indicating the missing artifact
        with pytest.raises(AirflowException) as exc_info:
            artifacts_publisher.copy_artifacts_from_git_to_s3(artifact_copy_info)

        # Verify the exception message indicates the missing artifact
        assert "Cannot find missing_config.yaml" in str(exc_info.value)

    @patch('dags.operators.artifacts_publisher.GitHook')
    @patch('dags.operators.artifacts_publisher.tempfile.TemporaryDirectory')
    @patch('os.path.exists')
    def test_download_ndc_lookup_file_missing_directory(
        self,
        mock_exists: Mock,
        mock_temp_dir: Mock,
        mock_git_hook_class: Mock,
        artifacts_publisher: ArtifactsPublisher,
        mock_filesystem: Mock
    ):
        """Test error handling when NDC lookup directory is missing."""

        # Given: An ArtifactsPublisher configured for NDC download with missing directory
        # simulating a repository structure issue or missing data catalog
        artifacts_publisher._fs = mock_filesystem
        artifacts_publisher.git_meta_repo = "test-meta-repo"

        # Mock temporary directory context manager
        temp_dir_mock = MagicMock()
        temp_dir_mock.__enter__.return_value = "/tmp/git"
        temp_dir_mock.__exit__.return_value = None
        mock_temp_dir.return_value = temp_dir_mock

        # Mock git hook context manager
        mock_git_hook = MagicMock()
        mock_git_hook.__enter__.return_value = mock_git_hook
        mock_git_hook.__exit__.return_value = None
        mock_git_hook.clone = Mock()
        mock_git_hook_class.return_value = mock_git_hook
        mock_exists.return_value = False  # NDC directory does not exist

        # When: The download_ndc_lookup_file method is called with missing directory
        # Then: An AirflowException should be raised indicating the missing directory
        with pytest.raises(AirflowException):
            artifacts_publisher.download_ndc_lookup_file()

    def test_execute_with_invalid_revision(
        self,
        artifacts_publisher: ArtifactsPublisher,
        mock_filesystem: Mock
    ):
        """Test execution failure with invalid revision format."""

        # Given: An ArtifactsPublisher with an invalid non-numeric revision
        # that should fail validation before any file operations
        artifacts_publisher.revision = "invalid_revision_format"
        artifacts_publisher._fs = mock_filesystem

        # When: The execute method is called with invalid revision
        # Then: An exception should be raised during revision validation
        with pytest.raises(Exception):
            artifacts_publisher.execute({})

        # Verify that no file operations were attempted after validation failure
        mock_filesystem.copy_files_parallel.assert_not_called()

    def test_fs_property_initialization(self, artifacts_publisher: ArtifactsPublisher):
        """Test lazy initialization of filesystem property."""

        # Given: An ArtifactsPublisher instance without pre-initialized filesystem
        # When: The fs property is accessed for the first time
        filesystem = artifacts_publisher.fs

        # Then: An AetionS3FileSystem instance should be created and cached
        assert filesystem is not None
        assert isinstance(filesystem, AetionS3FileSystem)

        # Subsequent access should return the same instance
        assert artifacts_publisher.fs is filesystem

    @patch('dags.operators.artifacts_publisher.tempfile.TemporaryDirectory')
    @patch('dags.operators.artifacts_publisher.zipfile.is_zipfile')
    @patch('dags.operators.artifacts_publisher.zipfile.ZipFile')
    def test_execute_with_zip_file_processing(
        self,
        mock_zipfile_class: Mock,
        mock_is_zipfile: Mock,
        mock_temp_dir: Mock,
        artifacts_publisher: ArtifactsPublisher,
        mock_filesystem: Mock
    ):
        """Test execution with zip file version extraction."""

        # Given: An ArtifactsPublisher with zip artifacts containing version information
        # simulating processing of packaged artifacts with embedded git versions
        artifacts_publisher.revision = None  # Skip revision check
        artifacts_publisher._fs = mock_filesystem

        # Mock temporary directory context manager
        temp_dir_mock = MagicMock()
        temp_dir_mock.__enter__.return_value = "/tmp/test"
        temp_dir_mock.__exit__.return_value = None
        mock_temp_dir.return_value = temp_dir_mock

        # Mock zip file operations
        mock_is_zipfile.return_value = True  # Simulate zip files

        # Mock zip file context manager with GIT_VERSION file
        mock_zipfile = MagicMock()
        mock_zipfile.__enter__.return_value = mock_zipfile
        mock_zipfile.__exit__.return_value = None
        mock_zipfile.extract = Mock()
        mock_zipfile_class.return_value = mock_zipfile

        # Mock file reading for version extraction
        with patch('builtins.open', mock_open(read_data="v1.2.3\n")):
            # When: The execute method processes zip artifacts
            # Then: Version extraction should complete without errors
            artifacts_publisher.execute({})

        # Verify zip file operations were performed
        mock_zipfile.extract.assert_called_with("GIT_VERSION")

    @patch('dags.operators.artifacts_publisher.tempfile.TemporaryDirectory')
    @patch('dags.operators.artifacts_publisher.zipfile.is_zipfile')
    @patch('dags.operators.artifacts_publisher.zipfile.ZipFile')
    def test_execute_zip_file_missing_version(
        self,
        mock_zipfile_class: Mock,
        mock_is_zipfile: Mock,
        mock_temp_dir: Mock,
        artifacts_publisher: ArtifactsPublisher,
        mock_filesystem: Mock
    ):
        """Test handling of zip files without GIT_VERSION file."""

        # Given: An ArtifactsPublisher with zip artifacts missing version information
        # simulating incomplete or legacy artifact packages
        artifacts_publisher.revision = None
        artifacts_publisher._fs = mock_filesystem

        # Mock temporary directory context manager
        temp_dir_mock = MagicMock()
        temp_dir_mock.__enter__.return_value = "/tmp/test"
        temp_dir_mock.__exit__.return_value = None
        mock_temp_dir.return_value = temp_dir_mock

        # Mock zip file operations
        mock_is_zipfile.return_value = True

        # Mock zip file context manager without GIT_VERSION file (raises KeyError)
        mock_zipfile = MagicMock()
        mock_zipfile.__enter__.return_value = mock_zipfile
        mock_zipfile.__exit__.return_value = None
        mock_zipfile.extract.side_effect = KeyError("GIT_VERSION not found")
        mock_zipfile_class.return_value = mock_zipfile

        # When: The execute method processes zip artifacts without version files
        # Then: The method should handle the missing version gracefully
        artifacts_publisher.execute({})  # Should not raise exception

        # Verify that processing continued despite missing version file
        mock_filesystem.copy_files_parallel.assert_called_once()

    def test_template_fields_configuration(self):
        """Test that template fields are properly configured for Airflow templating."""

        # Given: The ArtifactsPublisher class definition
        # When: Checking the template_fields configuration
        # Then: All necessary fields should be marked as templatable
        expected_template_fields = (
            "repo",
            "private_key",
            "git_default_branch",
            "branch",
            "global_artifacts_path",
            "dataset_artifacts_path",
            "rdc",
            "artifacts",
        )

        assert ArtifactsPublisher.template_fields == expected_template_fields

    def test_init_with_minimal_parameters(self, sample_artifacts: dict[str, bool]):
        """Test initialization with only required parameters."""

        # Given: Only the minimum required parameters for ArtifactsPublisher
        # When: The operator is instantiated with minimal configuration
        operator = ArtifactsPublisher(
            repo="test-repo",
            private_key="test-key",
            git_default_branch="main",
            branch="feature",
            global_artifacts_path="s3://global",
            dataset_artifacts_path="s3://dataset",
            rdc="catalog.yml",
            artifacts=sample_artifacts,
            task_id="minimal_test",
        )

        # Then: Default values should be properly set for optional parameters
        assert operator.aws_conn_id == "aws_default"
        assert operator.git_meta_repo is None
        assert operator.client is None
        assert operator.dataset is None
        assert operator.revision is None
        assert operator.git_config_file_path is None
        assert operator.config_file_url is None
