#!/usr/bin/env bash

set +e

TRY_LOOP="7"

echo "$(date) - COMMAND: $@"
echo "$(date) - EXECUTOR: $EXECUTOR"

: "${AIRFLOW__CORE__EXECUTOR:=${EXECUTOR:-Celery}Executor}"
echo "$(date) - $AIRFLOW__CORE__EXECUTOR"

export AIRFLOW__CORE__FERNET_KEY=${FERNET_KEY}
[ "$DEBUG_MODE" ] && echo "$(date) - AIRFLOW__CORE__FERNET_KEY: $AIRFLOW__CORE__FERNET_KEY"

export AIRFLOW__LOGGING__REMOTE_LOGGING=${S3_LOGGING}
export AIRFLOW__LOGGING__REMOTE_BASE_LOG_FOLDER=${S3_LOGGING_BASE_LOG_FOLDER}
export AIRFLOW__LOGGING__REMOTE_LOG_CONN_ID=airflow_s3_access

export \
  AIRFLOW__CELERY__BROKER_URL \
  AIRFLOW__CELERY__RESULT_BACKEND \
  AIRFLOW__CORE__EXECUTOR \
  AIRFLOW__CORE__LOAD_EXAMPLES \
  AIRFLOW__DATABASE__SQL_ALCHEMY_CONN

[ -z "$POSTGRES_HOST" ] && echo "$(date) - POSTGRES_HOST is not set"
[ -z "$POSTGRES_PORT" ] && echo "$(date) - POSTGRES_PORT is not set"
[ -z "$POSTGRES_DB" ] && echo "$(date) - POSTGRES_DB is not set"
[ -z "$POSTGRES_USER" ] && echo "$(date) - POSTGRES_USER is not set"
[ -z "$SLACK_TOKEN" ] && echo "$(date) - SLACK_TOKEN is not set, won't have slack notifications"

[ "$DEBUG_MODE" ] && echo "$(date) - GIT_PRIVATE_KEY: $GIT_PRIVATE_KEY"
[ "$DEBUG_MODE" ] && echo "$(date) - DATABRICKS_TOKEN: $DATABRICKS_TOKEN"
[ "$DEBUG_MODE" ] && echo "$(date) - POSTGRES_PASSWORD: $POSTGRES_PASSWORD"
[ "$DEBUG_MODE" ] && echo "$(date) - REDIS_PASSWORD: $REDIS_PASSWORD"

if [ "$AIRFLOW__CORE__EXECUTOR" = "LocalExecutor" ]; then
    echo "$(date) - Setting params for $AIRFLOW__CORE__EXECUTOR"

    [ -z "$POSTGRES_PASSWORD" ] && echo "POSTGRES_PASSWORD is not set"

    AIRFLOW__DATABASE__SQL_ALCHEMY_CONN="postgresql+psycopg2://$POSTGRES_USER:$POSTGRES_PASSWORD@$POSTGRES_HOST:$POSTGRES_PORT/$POSTGRES_DB"
    [ "$DEBUG_MODE" ] && echo "$(date) - AIRFLOW__DATABASE__SQL_ALCHEMY_CONN: $AIRFLOW__DATABASE__SQL_ALCHEMY_CONN"
else
     echo "$(date) - Setting params for $AIRFLOW__CORE__EXECUTOR"

    [ -z "$POSTGRES_HOST" ] && echo "\$POSTGRES_HOST is not set"
    [ -z "$POSTGRES_PORT" ] && echo "\$POSTGRES_PORT is not set"
    [ -z "$POSTGRES_USER" ] && echo "\$POSTGRES_USER is not set"
    [ -z "$POSTGRES_PASSWORD" ] && echo "\$POSTGRES_PASSWORD is not set"

    AIRFLOW__DATABASE__SQL_ALCHEMY_CONN="postgresql+psycopg2://$POSTGRES_USER:$POSTGRES_PASSWORD@$POSTGRES_HOST:$POSTGRES_PORT/$POSTGRES_DB"
    [ "$DEBUG_MODE" ] && echo "$(date) - AIRFLOW__DATABASE__SQL_ALCHEMY_CONN: $AIRFLOW__DATABASE__SQL_ALCHEMY_CONN"

    AIRFLOW__CELERY__RESULT_BACKEND="db+postgresql://$POSTGRES_USER:$POSTGRES_PASSWORD@$POSTGRES_HOST:$POSTGRES_PORT/$POSTGRES_DB"
    [ "$DEBUG_MODE" ] && echo "$(date) - AIRFLOW__CELERY__RESULT_BACKEND: $AIRFLOW__CELERY__RESULT_BACKEND"

    [ -z "$REDIS_HOST" ] && echo "$(date) - REDIS_HOST is not set"
    [ -z "$REDIS_PORT" ] && echo "$(date) - REDIS_PORT is not set"
    [ -z "$REDIS_USE_TLS" ] && echo "$(date) - REDIS_USE_TLS is not set"
    [ -z "$REDIS_PATH" ] && echo "$(date) - REDIS_PATH is not set, using /1"
    [ -z "$REDIS_PASSWORD" ] && echo "$(date) - REDIS_PASSWORD is not set"

    # If REDIS_USE_TLS is defined, use rediss://
    # If REDIS_USE_TLS is not defined, use rediss:// if REDIS_PASSWORD is set (for backwards compatability)
    if [ -n "$REDIS_USE_TLS" ]; then
        REDIS_PROTO="rediss"
	      REDIS_ARGS="?ssl_cert_reqs=optional"
    else
        REDIS_PROTO="redis"
    fi
    if [ -z "$REDIS_PATH" ]; then
        REDIS_PATH="/1"
    fi
    REDIS_LOC="$REDIS_HOST:$REDIS_PORT"
    if [ -n "$REDIS_PASSWORD" ]; then
        REDIS_LOC=":$REDIS_PASSWORD@$REDIS_LOC"
    fi
    AIRFLOW__CELERY__BROKER_URL="$REDIS_PROTO://$REDIS_LOC$REDIS_PATH$REDIS_ARGS"

    [ "$DEBUG_MODE" ] && echo "$(date) - AIRFLOW__CELERY__BROKER_URL: $AIRFLOW__CELERY__BROKER_URL"
fi

if [ -n "${SLACK_TOKEN:+x}" ]; then
  airflow connections delete slack_api_default
  airflow connections add slack_api_default  --conn-type slack --conn-password "${SLACK_TOKEN}"
fi

AIRFLOW__CORE__LOAD_EXAMPLES=False

# https://github.com/concord-workflow/ck8s/blob/main/flows/ck8s-components/ck8s-bash
function retry {
  local retries=$1
  shift

  local count=0
  echo "Will try: " "$@"
  until "$@"; do
    exit=$?
    wait=$((2 ** $count))
    count=$(($count + 1))
    if [ $count -lt "$retries" ]; then
      echo "Retry $count/$retries exited $exit, retrying in $wait seconds..."
      sleep $wait
    else
      echo "Retry $count/$retries exited $exit, no more retries left."
      return $exit
    fi
  done
  return 0
}



sync_dags() {
    echo "$(date) - sync_dags"

    echo "sync_dags: DAG's path is $DAGS_PATH "

    if [[ $DAGS_PATH = "" ]]
    then
      echo "$(date) - sync_dags: DAG's path (dags_path) is not set"
    else
        echo "$(date) - sync_dags: synching DAG's from $DAGS_PATH to ${AIRFLOW_HOME}/dags"
        aws s3 sync "$DAGS_PATH" "${AIRFLOW_HOME}"/dags/
    fi
}

case "$1" in
  webserver)
    if [ -f "${AIRFLOW_HOME}/dags/aetion/adip/version.txt" ]; then
        GIT_SHA=$(cat "${AIRFLOW_HOME}/dags/aetion/adip/version.txt")
        export AIRFLOW__WEBSERVER__INSTANCE_NAME="Release: $GIT_SHA"
        echo "$(date) - GIT_SHA: $GIT_SHA"
    fi

    echo "$(date) - webserver init"
    retry $TRY_LOOP python3 dags/tools/check_postgresql_connectivity.py || exit 1
    retry $TRY_LOOP python3 dags/tools/check_redis_connectivity.py || exit 1

    echo "$(date) - run airflow migrate"
    airflow db migrate
    echo "$(date) - create users"
    airflow users create -u automation -p Iforget4 -e NA -f NA -l NA -r Admin

    echo "$(date) - set airflow connection for databricks"
    echo "$(date) - delete databricks connection at airflow"
    airflow connections delete databricks_default
    echo "$(date) - add databricks connection at airflow"
    [ "$DEBUG_MODE" ] && echo "$(date) - databricks conn uri: databricks://token@${DATABRICKS_HOST}?host=${DATABRICKS_HOST}&token=${DATABRICKS_TOKEN}"
    airflow connections add databricks_default \
        --conn-uri "databricks://token@${DATABRICKS_HOST}?host=${DATABRICKS_HOST}&token=${DATABRICKS_TOKEN}"

    echo "$(date) - add airflow_s3_access connection for remote logging"
    #  https://airflow.apache.org/docs/apache-airflow-providers-amazon/stable/connections/aws.html#amazon-web-services-connection
    airflow connections delete airflow_s3_access
    airflow connections add airflow_s3_access --conn-uri aws://@/?region_name=${AWS_REGION}

    echo "$(date) - add aws_default connection for S3 operations"
    airflow connections delete aws_default
    airflow connections add aws_default --conn-uri aws://@/?region_name=${AWS_REGION}


    sync_dags

    if [ "$AIRFLOW__CORE__EXECUTOR" = "LocalExecutor" ];
    then
      # With the "Local" executor it should all run in one container.
      airflow scheduler &
    fi
    exec airflow webserver
    ;;
  scheduler)
    echo "$(date) - scheduler init"
    # this stunnel stuff is apparently not used
    echo "$(date) - Stating Redis TLS tunnel"
    stunnel /redis.conf
    netstat -tulnp | grep -i stunnel
    retry $TRY_LOOP python3 dags/tools/check_postgresql_connectivity.py || exit 1
    retry $TRY_LOOP python3 dags/tools/check_redis_connectivity.py || exit 1
    # To give the webserver time to run initdb.
    sleep 10
    sync_dags
    exec airflow scheduler
    ;;
  triggerer)
    echo "$(date) - triggerer init"
    # this stunnel stuff is apparently not used
    echo "$(date) - Stating Redis TLS tunnel"
    stunnel /redis.conf
    netstat -tulnp | grep -i stunnel
    retry $TRY_LOOP python3 dags/tools/check_postgresql_connectivity.py || exit 1
    retry $TRY_LOOP python3 dags/tools/check_redis_connectivity.py || exit 1
    # To give the webserver time to run initdb.
    sleep 10
    exec airflow triggerer
    ;;
  worker)
    echo "$(date) - worker init"
    echo "$(date) - Stating Redis TLS tunnel"
    # this stunnel stuff is apparently not used
    stunnel /redis.conf
    netstat -tulnp | grep -i stunnel
    retry $TRY_LOOP python3 dags/tools/check_postgresql_connectivity.py || exit 1
    retry $TRY_LOOP python3 dags/tools/check_redis_connectivity.py || exit 1
    # To give the webserver time to run initdb.
    sleep 10
    sync_dags
    exec airflow celery worker
    ;;
  flower)
    # this is likely unused
    echo "$(date) - flower init"
    retry $TRY_LOOP python3 dags/tools/check_redis_connectivity.py || exit 1
    exec airflow celery flower
    ;;
  version)
    echo "$(date) - version init"
    exec airflow "$@"
    ;;
  *)
    # The command is something like bash, not an airflow subcommand. Just run it in the right environment.
    exec "$@"
    ;;
esac
