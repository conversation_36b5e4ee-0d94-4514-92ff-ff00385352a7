defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: "optum_market_clarity_obesity"
client: "amgen"
revision: "********"
is_k8s: True
alert_user: "@evgeniy.varganov"
deployment_config:
  amgen: "amgen.aetion.com"
dynamic_patient_table: True

git_branch_override: "amgen-optum_market_clarity_obesity-********"
hive_vars:
  GDR_END_DATE: "2024-09-30"
  GDR_START_DATE: "2007-01-01"
  MASKED_BIRTH_DATE: "1930 and Earlier"
iam_arn: "arn:aws:iam::************:instance-profile/amgen-databricks-etl-iam"
pyspark_memory_overhead_factor: ".2"
regenerate_all_flat_tables: False
service_account: "spark-operator-client-amgen"
source_files_password: ""
spark_memory_overhead_factor: ".2"
transform_path: "humedica/optum_market_clarity_obesity/amgen/current"
upload_bucket: "amgen.aetion.com/upload/optum_market_clarity_obesity/********"
use_smart_sampling: False
validation_path: "humedica/optum_market_clarity_obesity/amgen/current"
validation_vars:
  GDR_END_DATE: "2024-09-30"
  GDR_START_DATE: "2007-01-01"
steps_spark_config:
  full_job:
    autoscale:
      enabled: True
      initial_executors: 60
      max_executors: 80
      min_executors: 5
    driver_conf:
      instance_type: "r5a.8xlarge"
      pyspark_memory_overhead: "54500m"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 60000
      spark.io.compression.codec: "snappy"
      spark.memory.offHeap.enabled: False
      spark.sql.broadcastTimeout: -1
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 1
    worker_conf:
      instance_type: "i3en.6xlarge"
  full_patient_job:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 100
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 140000
      spark.task.cpus: 2
    worker_conf:
      instance_type: "r5ad.8xlarge"
  full_shard_job:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 120
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      spark.task.cpus: 2
    worker_conf:
      instance_type: "r5ad.8xlarge"
  default:
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
