defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: marketscan
client: ucb
revision: "********"
is_k8s: true
alert_user: "@rafal.kwiatkowski"
pyspark_memory_overhead_factor: ".4"
spark_memory_overhead_factor: ".2"
deployment_config:
  ucb: ucb.aetion.com
dynamic_patient_table: true
preprocess: true
git_branch_override: ucb_marketscan_********
hive_vars:
  EV_START_DATE: "2024-09-30"
  EV_END_DATE: "2025-04-30"
  GDR_END_DATE: "2025-04-30"
  GDR_START_DATE: "2007-12-31"
service_account: spark-operator-client-ucb
regenerate_all_flat_tables: false
source_files_password: ""
transform_path: marketscan/ucb/current
upload_bucket: ucb.aetion.com/upload/marketscan/********
validation_path: marketscan/ucb/current
validation_vars:
  GDR_END_DATE: "2025-04-30"
  GDR_START_DATE: "2007-12-31"
use_smart_sampling: false
steps_spark_config:
  unarchiver_job_only:
    worker_conf:
      instance_type: c5.4xlarge
      memory_on_disk: 128G
  full_job:
    autoscale:
      enabled: true
      min_executors: 1
      initial_executors: 1
      max_executors: 20
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: ""
    spark_conf:
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.task.cpus: 1
      spark.sql.shuffle.partitions: 20000
  full_patient_job:
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    worker_conf:
      cores: 5
      instance_type: r5ad.4xlarge
      memory: "65g"
      memory_on_disk: "128G"
    autoscale:
      enabled: true
      min_executors: 1
      initial_executors: 1
      max_executors: 40
    spark_conf:
      spark.sql.shuffle.partitions: 10000
      spark.task.cpus: 2
      spark.sql.parquet.enableVectorizedReader: false
  full_shard_job:
    autoscale:
      enabled: true
      min_executors: 1
      initial_executors: 1
      max_executors: 80
    spark_conf:
      spark.task.cpus: 2
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: ""
  default:
    autoscale:
      enabled: true
      min_executors: 5
      initial_executors: 5
      max_executors: 5
    driver_conf:
      instance_type: r5ad.4xlarge
      pyspark_memory_overhead: 36500m
      spark_memory_overhead: 36500m
      memory_on_disk: ""
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: ""
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.sql.shuffle.partitions: 2000
      spark.sql.broadcastTimeout: 30000
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.driver.maxResultSize: 0
      spark.task.cpus: 1
