defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "rwd"
client: "chugai"
revision: "2021031502"
is_k8s: False
alert_user: "@alexandra.soboleva"
git_branch_override: "chugai_rwd_lung_20210315"
hive_vars:
  GDR_END_DATE: "2020-11-18"
  GDR_START_DATE: "1985-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/chugai-databricks-etl-iam"
pre_partitioned_data_url: "chugai.aetion.com/etl/rwd/20210315"
transform_path: "rwd/chugai/20210315"
upload_bucket: "chugai.aetion.com/upload/rwd/2021031502"
validation_path: "rwd/chugai20210315"
validation_vars:
  GDR_END_DATE: "2020-11-18"
  GDR_START_DATE: "1985-01-01"
steps_spark_config:
  full_shard_job:
    spark_conf:
      spark.driver.maxResultSize: "200g"
      spark.sql.shuffle.partitions: 1000
  default:
    spark_conf:
      spark.driver.maxResultSize: "50g"
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 800
