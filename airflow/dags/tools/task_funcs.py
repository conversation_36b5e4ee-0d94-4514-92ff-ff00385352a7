import json
import logging
from _collections import OrderedDict
from typing import List, Dict

from tools.config import GlobalConfig, PipelineConfig, CommonConfig, DagConfig, is_truthy
from operators.artifacts_publisher import NDC_CODING_SYSTEM_DEST_FILE
from airflow.utils.trigger_rule import TriggerRule
from airflow.operators.python import PythonOperator

class SparkTaskType:
    PYTHON = 'python'
    JVM = 'jvm'


def _jars(libs):
    jars = []

    for lib in libs:
        if lib.get('jar') is not None:
            jars.append(lib.get('jar'))

    return jars


def _eggs(libs):
    eggs = []

    for lib in libs:
        if lib.get('egg') is not None:
            eggs.append(lib.get('egg'))

    return eggs


class TaskCreator():
    def __init__(self, name_prefix, dag, git_creds: dict, log_func, default_failure_callback=None):
        self.is_k8s = None
        self.service_account = None

        self.spark_memory_overhead_factor = None
        self.pyspark_memory_overhead_factor = None

        self.name_prefix = name_prefix
        self.dag = dag
        self.git_creds = git_creds
        self.log_func = log_func
        self.default_failure_callback = default_failure_callback

    def set_k8s(self):
        self.is_k8s = True

    def set_service_account(self, service_account):
        self.service_account = service_account

    def set_pyspark_memory_overhead_factor(self, pyspark_memory_overhead_factor):
        self.pyspark_memory_overhead_factor = pyspark_memory_overhead_factor

    def set_spark_memory_overhead_factor(self, spark_memory_overhead_factor):
        self.spark_memory_overhead_factor = spark_memory_overhead_factor

    def task_name(self, name):
        return '{}{}'.format(self.name_prefix, name)

    def task_py_func(self, name, func, op_args=None, params=None, on_failure_callback=None):
        return PythonOperator(
            task_id=self.task_name(name),
            dag=self.dag,
            python_callable=func,
            op_args=op_args,
            params=params,
            on_failure_callback=on_failure_callback,
            # even if an upstream task was skipped, these tasks will run
            trigger_rule=TriggerRule.NONE_FAILED
        )

    def task_py(self, name, py_op, dict_pars, params=None, on_failure_callback=None, **kwargs):
        return py_op(
            task_id=self.task_name(name),
            dag=self.dag,
            # even if an upstream task was skipped, these tasks will run
            trigger_rule=TriggerRule.NONE_FAILED,
            params=params,
            on_failure_callback=on_failure_callback,
            **kwargs,
            **dict_pars,
            **self.git_creds)

    def task_jvmspark(self, name, cluster_params, class_name, ar_pars, extra_params=None, on_failure_callback=None):
        # Use default failure callback if none provided
        if on_failure_callback is None:
            on_failure_callback = self.default_failure_callback

        if self.is_k8s:
            libs = cluster_params.get('libraries')

            jars = _jars(libs)

            main_application = jars[0] if len(jars) > 0 else None
            override_java_libraries = jars[1:]

            override_python_libraries = _eggs(libs)

            return self.log_func(
                self.task_name(name),
                service_account=self.service_account,
                main_java_class=class_name,
                main_application=main_application,
                java_libraries=override_java_libraries,
                python_libraries=override_python_libraries,
                parameters=ar_pars,
                spark_conf=cluster_params.get('new_cluster').get('spark_conf', {}),
                hadoop_conf=cluster_params.get('new_cluster').get('hadoop_conf', {}),
                driver_conf=cluster_params.get('new_cluster').get('driver_conf', {}),
                worker_conf=cluster_params.get('new_cluster').get('worker_conf', {}),
                num_workers=cluster_params.get('new_cluster').get('num_workers', {}),
                autoscale=cluster_params.get('new_cluster').get('autoscale', {}),
                cluster_custom_tags=cluster_params['new_cluster']['custom_tags'],
                aws_zone=cluster_params['new_cluster']['aws_attributes']['zone_id'],
                memory_overhead_factor=self.spark_memory_overhead_factor,
                driver_memory_overhead=cluster_params.get('new_cluster').get('driver_conf', {}).get('spark_memory_overhead', ''),
                worker_memory_overhead=cluster_params.get('new_cluster').get('worker_conf', {}).get('spark_memory_overhead', ''),
                extra_params=extra_params,
                on_failure_callback=on_failure_callback
            )
        else:
            return self.log_func(
                self.task_name(name),
                cluster_params,
                {
                    "spark_jar_task": {
                        "main_class_name": class_name,
                        "parameters": ar_pars
                    }
                },
                on_failure_callback=on_failure_callback
            )

    def task_jvmspark_ex(self, name, cluster_params, class_name: str, op_args: List[str], op_kwargs: Dict[str, str], on_failure_callback=None):
        """
        Run an entrypoint on databricks. Allows for named arguments.
        """
        # Use default failure callback if none provided
        if on_failure_callback is None:
            on_failure_callback = self.default_failure_callback

        kwargs = []
        for key, value in op_kwargs.items():
            kwargs.append(f"--{key}")
            kwargs.append(value)

        if self.is_k8s:
            logging.info(f"k8s spark cluster tags: {cluster_params['new_cluster']['custom_tags']}")

            libs = cluster_params.get('libraries')

            jars = _jars(libs)

            main_application = jars[0] if len(jars) > 0 else None
            override_java_libraries = jars[1:]

            override_python_libraries = _eggs(libs)

            return self.log_func(
                self.task_name(name),
                service_account=self.service_account,
                main_java_class=class_name,
                main_application=main_application,
                java_libraries=override_java_libraries,
                python_libraries=override_python_libraries,
                parameters=op_args + kwargs,
                spark_conf=cluster_params.get('new_cluster').get('spark_conf', {}),
                hadoop_conf=cluster_params.get('new_cluster').get('hadoop_conf', {}),
                driver_conf=cluster_params.get('new_cluster').get('driver_conf', {}),
                worker_conf=cluster_params.get('new_cluster').get('worker_conf', {}),
                num_workers=cluster_params.get('new_cluster').get('num_workers', {}),
                autoscale=cluster_params.get('new_cluster').get('autoscale', {}),
                cluster_custom_tags=cluster_params['new_cluster']['custom_tags'],
                aws_zone=cluster_params['new_cluster']['aws_attributes']['zone_id'],
                memory_overhead_factor=self.spark_memory_overhead_factor,
                driver_memory_overhead=cluster_params.get('new_cluster').get('driver_conf', {}).get('spark_memory_overhead', ''),
                worker_memory_overhead=cluster_params.get('new_cluster').get('worker_conf', {}).get('spark_memory_overhead', ''),
                on_failure_callback=on_failure_callback
            )
        else:
            return self.log_func(
                self.task_name(name),
                cluster_params,
                {
                    "spark_jar_task": {
                        "main_class_name": class_name,
                        # List arguments will come before keyword arguments
                        "parameters": op_args + kwargs
                    }
                },
                on_failure_callback=self.default_failure_callback
            )

    def task_pyspark(self, name, cluster_params, class_name, ar_pars, on_failure_callback=None):
        # Use default failure callback if none provided
        if on_failure_callback is None:
            on_failure_callback = self.default_failure_callback

        if self.is_k8s:
            logging.info(f"k8s spark cluster tags: {cluster_params['new_cluster']['custom_tags']}")

            libs = cluster_params.get('libraries')

            override_python_libraries = _eggs(libs)
            override_java_libraries = _jars(libs)

            return self.log_func(
                self.task_name(name),
                service_account=self.service_account,
                main_java_class="",
                main_application=class_name,  # done
                python_libraries=override_python_libraries,
                java_libraries=override_java_libraries,
                parameters=ar_pars,
                spark_conf=cluster_params.get('new_cluster').get('spark_conf', {}),
                hadoop_conf=cluster_params.get('new_cluster').get('hadoop_conf', {}),
                driver_conf=cluster_params.get('new_cluster').get('driver_conf', {}),
                worker_conf=cluster_params.get('new_cluster').get('worker_conf', {}),
                num_workers=cluster_params.get('new_cluster').get('num_workers', {}),
                autoscale=cluster_params.get('new_cluster').get('autoscale', {}),
                cluster_custom_tags=cluster_params['new_cluster']['custom_tags'],
                aws_zone=cluster_params['new_cluster']['aws_attributes']['zone_id'],
                memory_overhead_factor=self.pyspark_memory_overhead_factor,
                driver_memory_overhead=cluster_params.get('new_cluster').get('driver_conf', {}).get('pyspark_memory_overhead', ''),
                worker_memory_overhead=cluster_params.get('new_cluster').get('worker_conf', {}).get('pyspark_memory_overhead', ''),
                on_failure_callback=on_failure_callback
            )
        else:
            return self.log_func(
                self.task_name(name),
                cluster_params,
                {
                    "spark_python_task": {
                        "python_file": class_name,
                        "parameters": ar_pars
                    }
                },
                on_failure_callback=on_failure_callback
            )

    def task_py_wheel(self, name, cluster_params, package_name, ar_pars):
        return self.log_func(
            self.task_name(name),
            cluster_params,
            {
                "python_wheel_task": {
                    "package_name": package_name,
                    "entry_point": "main",
                    "parameters": ar_pars
                }
            })


def _dump_catalog_overrides_json(catalog_overrides):
    if not catalog_overrides:
        return ''
    return json.dumps(catalog_overrides)


class ParamsCreator():
    def __init__(
            self,
            gc: GlobalConfig,
            pc: PipelineConfig,
            cc: CommonConfig,
            dc: DagConfig):
        self.gc, self.pc, self.cc, self.dc = gc, pc, cc, dc
        self.partition_params = OrderedDict(
            a0=cc.raw_url_s3,
            a1=cc.rdc_url_s3,
            a2=pc.client,
            a3=pc.dataset,
            a4=json.dumps(pc.base_params.get("base_paths", [])),
            a5=pc.base_params.get("revision_start_date", ""))

    def stopgap(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return dict(fail_stopgap=pipeline_config.fail_stopgap)

    def stopgap_check_gdr_and_spec_updates(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return dict(
            dataset=pipeline_config.dataset,
            client=pipeline_config.client,
            revision=pipeline_config.revision,
            alert_user=pipeline_config.alert_user,
            alert_scientist=pipeline_config.alert_scientist,
            fail_stopgap=pipeline_config.fail_stopgap,
            s3_bucket=pipeline_config.s3_bucket,
            preprocess=pipeline_config.preprocess,
            catalog_overrides=pipeline_config.catalog_overrides,
            partition_full_parquet_url=pipeline_config.partition_full_parquet_url,
        )

    def publish_artifacts(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        logging.info(f"PipelineConfig: {pipeline_config}")
        params = OrderedDict(
            global_artifacts_path=global_config.global_artifacts_path,
            dataset_artifacts_path=common_config.artifacts_url_s3,
            rdc=global_config.rdc,
            revision=pipeline_config.revision,
            client=pipeline_config.client,
            dataset=pipeline_config.dataset,
            artifacts=global_config.artifacts
        )

        return params

    def unarchiver(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            a0='unarchiver.unarchiver',
            a1=common_config.upload_path_url,
            a2=common_config.raw_url,
            a3=pipeline_config.source_files_password)

    def file_validator(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            raw_data_path=common_config.raw_url,
            raw_data_inventory_summary_path=common_config.raw_inventory_summary_url,
            rdc_path=common_config.rdc_url_s3,
            client=pipeline_config.client,
            dataset=pipeline_config.dataset)

    def table_level_report_comparison(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            a0=common_config.raw_url_s3,
            a1=common_config.rdc_url_s3,
            a2=pipeline_config.client,
            a3=pipeline_config.dataset,
            a4=pipeline_config.revision,
            a5=common_config.target_url,
            a6=_dump_catalog_overrides_json(pipeline_config.catalog_overrides))

    def create_metadata(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            a0=common_config.raw_url_s3,
            a1=pipeline_config.dataset,
            a2=pipeline_config.client,
            a3=common_config.rdc_url_s3,
            a4=common_config.metadata_url_s3,
            a5="false",  # for create_metadata we use raw tables
            a6=_dump_catalog_overrides_json(pipeline_config.catalog_overrides),
            )

    def create_adm_metadata(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            a0=common_config.partition_url_s3,
            a1=pipeline_config.dataset,
            a2=pipeline_config.client,
            a3=common_config.rdc_url_s3,
            a4=common_config.metadata_url_s3,
            a5="true",  # load partitioned data for create_adm_metadata
            a6=_dump_catalog_overrides_json(pipeline_config.catalog_overrides),
        )

    def create_full_adm_metadata(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            a0=common_config.partition_full_url_s3,
            a1=pipeline_config.dataset,
            a2=pipeline_config.client,
            a3=common_config.rdc_url_s3,
            a4=common_config.metadata_url_s3,
            a5="true",  # load partitioned data for create_adm_metadata
            a6=_dump_catalog_overrides_json(pipeline_config.catalog_overrides),
        )

    def create_raw_data_exploration(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        if is_truthy(pipeline_config.preprocess):
            data_s3_url = common_config.preprocessed_full_url_s3
        else:
            data_s3_url = common_config.partition_full_url_s3
        return OrderedDict(
            a0=data_s3_url,
            a1=common_config.etl_url_s3,
            a2=pipeline_config.dataset,
            a3=pipeline_config.client,
            a4=pipeline_config.revision,
            a5=common_config.rdc_url_s3,
            a6=_dump_catalog_overrides_json(pipeline_config.catalog_overrides),
            a7=pipeline_config.preprocess,
            a8=dag_config.raw_data_exploration_conf_url_s3,
            a9=pipeline_config.rde_patient_id,
            a10=pipeline_config.tpa_start or '',
            a11=pipeline_config.tpa_end or '',
        )

    def create_smart_sample(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            a0=common_config.partition_full_url_s3,
            a1=common_config.rdc_url_s3,
            a2=pipeline_config.client,
            a3=pipeline_config.dataset,
            a4=common_config.partition_url_s3,
            a5=common_config.sample_summary_url,
            a6=_dump_catalog_overrides_json(pipeline_config.catalog_overrides))

    def create_single_partition(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        d = OrderedDict(b0=common_config.partition_url_s3,
                        b1=_dump_catalog_overrides_json(self.pc.catalog_overrides))
        d.update(self.partition_params)
        d.move_to_end('b0')
        d.move_to_end('b1')

        return d

    def create_full_partition(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        d = OrderedDict(b0=common_config.partition_full_url_s3,
                        b1=_dump_catalog_overrides_json(self.pc.catalog_overrides))
        d.update(self.partition_params)
        d.move_to_end('b0')
        d.move_to_end('b1')
        return d

    def create_comparison_report(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            a0=common_config.metadata_url_s3 + 'table.meta.json',
            a1=pipeline_config.revision,
            a2=common_config.target_url)

    def create_single_preprocessed_tables(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            a0=common_config.partition_url_s3,
            a1=common_config.rdc_url_s3,
            a2=pipeline_config.client,
            a3=pipeline_config.dataset,
            a4=common_config.preprocessed_url_s3,
            a5=dag_config.preprocessed_sql_url_s3,
            a6=""
        )

    def create_full_preprocessed_tables(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            a0=common_config.partition_full_url_s3,
            a1=common_config.rdc_url_s3,
            a2=pipeline_config.client,
            a3=pipeline_config.dataset,
            a4=common_config.preprocessed_full_url_s3,
            a5=dag_config.preprocessed_sql_url_s3,
            a6=_dump_catalog_overrides_json(pipeline_config.catalog_overrides),
        )

    def rde_configuration_prep(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            s3_bucket=pipeline_config.s3_bucket,
            s3_path=dag_config.raw_data_exploration_conf_path,
            transform_path=pipeline_config.transform_path)

    def preprocess_prep(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            s3_bucket=pipeline_config.s3_bucket,
            s3_transformation_path=dag_config.data_transformation_path,
            transform_path=pipeline_config.transform_path)

    def copy_spec(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        logging.info(f"Global: {global_config}")
        return OrderedDict(
            global_artifacts_path=global_config.global_artifacts_path,
            dataset_artifacts_path=common_config.artifacts_url_s3,
            client=pipeline_config.client,
            dataset=pipeline_config.dataset,
            revision=pipeline_config.revision,
            use_copy_spec=pipeline_config.copy_spec,
            gdrive_credentials=global_config.gdrive_credentials
        )

    def patient_prep(self, validate_sql_dir=True):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            s3_bucket=pipeline_config.s3_bucket,
            s3_transformation_path=dag_config.data_transformation_path,
            transform_path=pipeline_config.transform_path,
            full_shard_enums_path=dag_config.full_shard_enums_path,
            rdc_path=common_config.rdc_url_s3,
            client=pipeline_config.client,
            dataset=pipeline_config.dataset,
            revision=pipeline_config.revision,
            transform_path_override=pipeline_config.transform_path_override,
            validate_sql_dir=validate_sql_dir
        )

    def dynamic_gdr_enabled(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return dag_config.dynamic_gdr_enabled

    def dynamic_gdr_configuration(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            a0=dag_config.gdr_template_sql_url_s3,
            a1=common_config.partition_full_url_s3,
            a2=pipeline_config.revision,
            a3=common_config.rdc_url_s3,
            a4=pipeline_config.client,
            a5=pipeline_config.dataset,
            a6=json.dumps(dag_config.hive_vars),
            a7=_dump_catalog_overrides_json(pipeline_config.catalog_overrides),
        )

    def gdr_configuration(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        # params for GdrConfiguration in gdr_configuration.py
        return OrderedDict(
            gdr_template_url=dag_config.gdr_template_sql_url_s3,
            revision=pipeline_config.revision,
            client=pipeline_config.client,
            dataset=pipeline_config.dataset,
            hive_vars=json.dumps(dag_config.hive_vars),
            validation_vars=json.dumps(dag_config.validation_vars),
            gdr_validation_url=dag_config.gdr_validation_url)

    def validation_prep(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return dict(
            s3_bucket=pipeline_config.s3_bucket,
            s3_path=dag_config.data_validation_path,
            gdr_sql=dag_config.gdr_sql_path,
            validation_path=pipeline_config.validation_path,
            validation_repo_root=pipeline_config.validation_repo_root,
            domain=pipeline_config.domain,
        )

    def validate_ndc_linkage(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            a0=dag_config.data_transformation_url_s3,
            a1=dag_config.full_shard_url_s3,
            a2=dag_config.ndc_linkage_report_url_s3,
            a3=f"{common_config.artifacts_url_s3}{NDC_CODING_SYSTEM_DEST_FILE}",
            a4=global_config.ndc_linkage_threshold)

    def validate_single_shard_ndc_linkage(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            a0=dag_config.data_transformation_url_s3,
            a1=dag_config.single_shard_url_s3,
            a2=dag_config.ndc_linkage_report_url_s3,
            a3=f"{common_config.artifacts_url_s3}{NDC_CODING_SYSTEM_DEST_FILE}",
            a4=global_config.ndc_linkage_threshold)

    def create_single_patient(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        if is_truthy(pipeline_config.preprocess):
            data_s3_url = common_config.preprocessed_url_s3
        else:
            data_s3_url = common_config.partition_url_s3
        params = OrderedDict(
            a0=data_s3_url,
            a1=common_config.rdc_url_s3,
            a2=pipeline_config.client,
            a3=pipeline_config.dataset,
            a4=dag_config.single_shard_patient_url_s3,
            a5=dag_config.patient_sql_url_s3,
            # partitionNum, spark default
            a6=200,
            a7='true',
            a8=str(pipeline_config.preprocess).lower(),
            a9=str(pipeline_config.dynamic_patient_table).lower()
        )

        if is_truthy(pipeline_config.dynamic_flat_tables):
            params["a10"] = str(pipeline_config.dynamic_flat_tables).lower()
            params["a11"] = f"{common_config.artifacts_url_s3}/coding_systems.csv"
            params["a12"] = f"{common_config.artifacts_url_s3}/data_specification.xlsx"

        return params

    def create_single_shard(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            a0=dag_config.data_transformation_url_s3,
            a1=dag_config.single_shard_url_s3,
            a2=dag_config.single_shard_patient_url_s3,
            a3=common_config.rdc_url_s3,
            a4=pipeline_config.client,
            a5=pipeline_config.dataset,
            # partitionNum, single partition
            a6=1)

    def profile_single_shard(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            a0=dag_config.single_shard_shard_url_s3)

    def generate_full_shard_validation_tables(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        if is_truthy(pipeline_config.preprocess):
            data_s3_url = common_config.preprocessed_full_url_s3
        else:
            data_s3_url = common_config.partition_full_url_s3
        return OrderedDict(
            a0='rvf.generate_validation_tables',
            a1=data_s3_url,
            a2=dag_config.data_full_validation_rules_url_s3,
            a3=dag_config.full_shard_shard_report_url_s3,
            a4=common_config.rdc_url_s3,
            a5=pipeline_config.client,
            a6=pipeline_config.dataset,
            a7=_dump_catalog_overrides_json(pipeline_config.catalog_overrides),
            a8=is_truthy(pipeline_config.preprocess))

    def validate_full_shard(self, job_submit_params):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc

        enable_deduplication = job_submit_params. \
            get('new_cluster', {}). \
            get('spark_conf', {}). \
            get('aetion.dataset.enableDeduplication', None)

        single_shard_message_log_url_s3 = dag_config.full_shard_message_log_url_s3. \
            replace('message.log', 'message-raw.log') \
            if enable_deduplication is not None and is_truthy(enable_deduplication) \
            else dag_config.full_shard_message_log_url_s3

        return OrderedDict(
            data_validation_rules_url_s3=dag_config.data_full_validation_rules_url_s3,
            single_shard_shard_profile_url_s3='UNUSED',
            single_shard_shard_report_url_s3=dag_config.full_shard_shard_report_url_s3,
            validation_vars=json.dumps(dag_config.validation_vars),
            single_shard_message_log_url_s3=single_shard_message_log_url_s3,
            is_adm=pipeline_config.native_data_for_adm_url is not None
        )

    def validate_single_shard(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            data_validation_rules_url_s3=dag_config.data_validation_rules_url_s3,
            single_shard_shard_profile_url_s3=dag_config.single_shard_shard_profile_url_s3,
            single_shard_shard_report_url_s3=dag_config.single_shard_shard_report_url_s3,
            validation_vars=json.dumps(dag_config.validation_vars),
            single_shard_message_log_url_s3=dag_config.single_shard_message_log_url_s3
        )

    def validate_ds(self):
        # repo,git_meta_repo,private_key,git_default_branch,single_shard_shard_url_s3
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return [
            common_config.upload_path_url,
            pipeline_config.domain,
        ]

    def commit_metadata(self):
        # repo,git_meta_repo,private_key,git_default_branch,single_shard_shard_url_s3
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return [
            common_config.upload_path_url,
            pipeline_config.transform_path,
            pipeline_config.validation_path,
            pipeline_config.domain,
        ]

    def filter_patient(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            super_patient_sql_url=f"s3a://{pipeline_config.super_patient_full_shard_s3_path}patient/",
            pruned_patient_sql_url=dag_config.full_shard_url_s3,
            transformation_url=dag_config.data_transformation_url_s3
        )

    def validate_single_shard_parquet(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        parameters = OrderedDict()
        # confusingly, data format validation needs the fully-qualified URL which is stored in common_config.etl_url
        parameters["etl-path"] = f"s3://{common_config.etl_url}"
        parameters["mode"] = "single-shard"

        return parameters

    def comprehensive_validate_full_shard(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            data_validation_rules_url_s3=dag_config.data_validation_rules_url_s3,
            single_shard_shard_profile_url_s3=dag_config.full_shard_shard_profile_url_s3,
            single_shard_shard_report_url_s3=dag_config.full_shard_shard_report_url_s3,
            validation_vars=json.dumps(dag_config.validation_vars),
            single_shard_message_log_url_s3=dag_config.full_shard_message_log_url_s3,
            is_adm=pipeline_config.native_data_for_adm_url is not None
        )

    def generate_single_shard_validation_tables(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        if is_truthy(pipeline_config.preprocess):
            data_s3_url = common_config.preprocessed_url_s3
        else:
            data_s3_url = common_config.partition_url_s3
        return OrderedDict(
            a0='rvf.generate_validation_tables',
            a1=data_s3_url,
            a2=dag_config.data_validation_rules_url_s3,
            a3=dag_config.single_shard_shard_report_url_s3,
            a4=common_config.rdc_url_s3,
            a5=pipeline_config.client,
            a6=pipeline_config.dataset,
            a7="",
            a8=is_truthy(pipeline_config.preprocess)
        )

    def full_validation_prep(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return dict(
            s3_bucket=pipeline_config.s3_bucket,
            s3_path=dag_config.full_data_validation_path,
            gdr_sql=dag_config.gdr_sql_path,
            validation_path=pipeline_config.full_validation_path,
            validation_repo_root=pipeline_config.full_validation_repo_root,
            domain=pipeline_config.domain,
        )

    def generate_full_enums_and_dictionaries(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            a0=common_config.partition_full_url_s3,
            a1=common_config.rdc_url_s3,
            a2=pipeline_config.client,
            a3=pipeline_config.dataset,
            a4=dag_config.full_shard_patient_url_s3,
            a5=dag_config.patient_sql_url_s3,
            # partitionNum, no value set
            a6=0)

    def automatically_update_enums(self):
        from adip_common.path_config.path_manager import ADIPPathManager, PathFormat
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        adip_path_manager = ADIPPathManager(
            s3_bucket=pipeline_config.s3_bucket,
            dataset=pipeline_config.dataset,
            revision=pipeline_config.revision,
            path_format=PathFormat.S3A
        )
        return OrderedDict(
            transform_path=pipeline_config.transform_path,
            full_shard_enums_path=adip_path_manager.get_path("paths.full_shard.enums.enums_csv"),
            dataset_artifacts_path=common_config.artifacts_url_s3,
            client=pipeline_config.client,
            dataset=pipeline_config.dataset,
            revision=pipeline_config.revision
        )

    def delete_full_flat_tables(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            full_shard_url=dag_config.full_shard_url_s3,
            regenerate_all_flat_tables=pipeline_config.regenerate_all_flat_tables,
            flat_tables_to_regenerate=pipeline_config.flat_tables_to_regenerate)

    def generate_full_flat_tables(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        if is_truthy(pipeline_config.preprocess):
            data_s3_url = common_config.preprocessed_full_url_s3
        else:
            data_s3_url = common_config.partition_full_url_s3
        params = OrderedDict(
            a0=data_s3_url,
            a1=common_config.rdc_url_s3,
            a2=pipeline_config.client,
            a3=pipeline_config.dataset,
            a4=dag_config.full_shard_patient_url_s3,
            a5=dag_config.patient_sql_url_s3)


        if is_truthy(pipeline_config.dynamic_flat_tables):
            params["a6"] = str(pipeline_config.dynamic_flat_tables).lower()
            params["a7"] = f"{common_config.artifacts_url_s3}/coding_systems.csv"
            params["a8"] = f"{common_config.artifacts_url_s3}/data_specification.xlsx"
            params["a9"] = _dump_catalog_overrides_json(pipeline_config.catalog_overrides)
        else:
            params["a6"] = "false"
            params["a7"] = ""
            params["a8"] = ""
            params["a9"] = _dump_catalog_overrides_json(pipeline_config.catalog_overrides)

        params["a10"] = pipeline_config.preprocess
        params["a11"] = pipeline_config.regenerate_all_flat_tables
        params["a12"] = ";".join(pipeline_config.flat_tables_to_regenerate)
        return params

    def generate_full_patient(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            a0=common_config.partition_full_url_s3,
            a1=common_config.rdc_url_s3,
            a2=pipeline_config.client,
            a3=pipeline_config.dataset,
            a4=dag_config.full_shard_patient_url_s3,
            a5=dag_config.patient_sql_url_s3,
            # partitionNum, no value set
            a6=0,
            a7=str(pipeline_config.dynamic_patient_table).lower()
        )

    def generate_sampled_data(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            a0=dag_config.data_transformation_url_s3,
            a1=dag_config.full_shard_url_s3,
            a2=dag_config.full_shard_url_s3,
            a3=dag_config.sampled_data_url_s3,
            a4=common_config.rdc_url_s3,
            a5=pipeline_config.sampled_data_percentage,
            a6=pipeline_config.client,
            a7=pipeline_config.dataset)

    def create_full_shard(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            a0=dag_config.data_transformation_url_s3,
            a1=dag_config.full_shard_url_s3,
            a2=dag_config.full_shard_patient_url_s3,
            a3=common_config.rdc_url_s3,
            a4=pipeline_config.client,
            a5=pipeline_config.dataset,
            # partitionNum, no value set
            a6=0)

    def profile_full_shard(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            a0=dag_config.full_shard_shard_url_s3)

    def generate_full_shard_validation_tables_no_message_log(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        if is_truthy(pipeline_config.preprocess):
            data_s3_url = common_config.preprocessed_full_url_s3
        else:
            data_s3_url = common_config.partition_full_url_s3
        return OrderedDict(
            a0='rvf.generate_validation_tables',
            a1=data_s3_url,
            a2=dag_config.data_validation_rules_url_s3,
            a3=dag_config.full_shard_shard_report_url_s3,
            a4=common_config.rdc_url_s3,
            a5=pipeline_config.client,
            a6=pipeline_config.dataset,
            a7=_dump_catalog_overrides_json(pipeline_config.catalog_overrides),
            a8=is_truthy(pipeline_config.preprocess))

    def compare_previous_full_shard_counts(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return OrderedDict(
            revision=pipeline_config.revision,
            target_url=common_config.target_url,
            data_cut_name=dag_config.data_cut_name
        )

    def generate_crosswalk(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc

        return OrderedDict(
            a0='adip_utilities.crosswalk_generator',
            a1=dag_config.full_shard_url_s3,
            a2=dag_config.full_shard_crosswalk_url_s3)

    def deploy_package_build(self):
        global_config, pipeline_config, common_config, dag_config = self.gc, self.pc, self.cc, self.dc
        return dict(
            data_transformation_url_s3=dag_config.data_transformation_url_s3,
            dataset_artifacts_path=common_config.artifacts_url_s3,
            full_shard_url=dag_config.full_shard_url,
            gdr_sql=dag_config.gdr_sql_url,
            gdr_validation=dag_config.gdr_validation_url,
            client=pipeline_config.client,
            transform_path=pipeline_config.transform_path,
            transform_path_override=pipeline_config.transform_path_override,
            deployment_config=pipeline_config.deployment_config,
            deployment_params=pipeline_config.deployment_params,
        )

    def full_shard_stopgap(self):
        return [self.pc.full_shard_stopgap]

    def get_callback_default_params(self):
        return {
            "s3_bucket": self.pc.s3_bucket,
            "dataset": self.pc.dataset,
            "alert_user": self.pc.alert_user,
            "revision": self.pc.revision
        }
