import csv
import logging
import os
import tempfile
from typing import Any
import zipfile
from collections import namedtuple

from airflow.exceptions import AirflowException
from airflow.models import BaseOperator
from aetion.adip.airflow.integrations.s3.s3_support import AetionS3FileSystem
from hooks.git_hook import GitHook


logger = logging.getLogger(__name__)

DATA_CATALOG_ROOT = "data-catalog"
CODING_SYSTEMS_ARTIFACT = "coding_systems.csv"
# note: the 2014 really means nothing, it's here because of hysterical raisins
NDC_CODING_SYSTEM = "NDC_FDB_2014"
NDC_CODING_SYSTEM_DEST_FILE = "ndc_fdb.csv.gz"


class ArtifactsPublisher(BaseOperator):
    """
    Copy artifacts from a airflow-global location to a pipeline-local location.
    """

    template_fields = (
        "repo",
        "private_key",
        "git_default_branch",
        "branch",
        "global_artifacts_path",
        "dataset_artifacts_path",
        "rdc",
        "artifacts",
    )
    template_ext = ()

    def __init__(
        self,
        repo: str,
        private_key: str,
        git_default_branch: str,
        branch: str,
        global_artifacts_path: str,
        dataset_artifacts_path: str,
        rdc: str,
        artifacts: dict[str, bool],
        aws_conn_id: str = "aws_default",
        git_meta_repo: str | None = None,
        client: str | None = None,
        dataset: str | None = None,
        revision: str | None = None,
        git_config_file_path: str | None = None,
        config_file_url: str | None = None,
        *args,
        **kwargs,
    ):
        super(ArtifactsPublisher, self).__init__(*args, **kwargs)
        self.repo = repo
        self.git_meta_repo = git_meta_repo
        self.private_key = private_key
        self.git_default_branch = git_default_branch
        self.branch = branch
        self.global_artifacts_path = global_artifacts_path
        self.dataset_artifacts_path = dataset_artifacts_path
        self.rdc = rdc
        self.client = client
        self.dataset = dataset
        self.revision = revision
        self.artifacts = artifacts

        self.git_config_file_path = git_config_file_path
        self.config_file_url = config_file_url
        self.aws_conn_id = aws_conn_id
        self._fs: AetionS3FileSystem | None = None

    @property
    def fs(self) -> AetionS3FileSystem:
        """
        Lazy-initialized S3 filesystem interface.

        This property provides access to the S3 filesystem operations through
        the AetionS3FileSystem wrapper. The filesystem instance is created
        only when first accessed to optimize resource usage.

        Returns
        -------
        AetionS3FileSystem
            Configured S3 filesystem interface using the specified AWS connection

        Notes
        -----
        - Uses lazy initialization to avoid unnecessary S3 connections
        - Reuses the same instance across multiple method calls
        - Configured with the aws_conn_id specified during operator initialization

        Examples
        --------
        Access filesystem for operations::

            TBD
        """
        if self._fs is None:
            self._fs = AetionS3FileSystem(aws_conn_id=self.aws_conn_id)
        return self._fs

    def execute(self, context: dict | Any = {}):
        logger.info("Validate revision is numeric")
        if self.revision:
            self.check_revision()

        logger.info(
            f"Publish artifacts from {self.global_artifacts_path} "
            f"to {self.dataset_artifacts_path}"
        )
        self.fs.copy_files_parallel(
            self.global_artifacts_path,
            self.dataset_artifacts_path,
            list(self.artifacts.keys()),
            8,
        )

        # copy ama artifacts subfolder containing ama catalog files
        self.fs.copy_with_pattern_parallel(
            os.path.join(self.global_artifacts_path, "ama"),
            os.path.join(self.dataset_artifacts_path, "ama"),
            ".*",
            8,
        )

        # copy prophecy artifacts subfolder containing ama catalog files
        self.fs.copy_with_pattern_parallel(
            os.path.join(self.global_artifacts_path, "prophecy"),
            os.path.join(self.dataset_artifacts_path, "prophecy"),
            ".*",
            8,
        )

        with tempfile.TemporaryDirectory() as tmpdir:
            logger.info(f"Downloading files {self.artifacts.keys()}")
            self.fs.download_files_parallel(
                self.dataset_artifacts_path, tmpdir, list(self.artifacts.keys()), 8
            )
            for artifact in self.artifacts.keys():
                artifact_path = os.path.join(tmpdir, artifact)
                if not zipfile.is_zipfile(artifact_path):
                    logger.info(f"{artifact_path} is not a zip-like file")
                    continue
                with zipfile.ZipFile(artifact_path) as zf:
                    try:
                        zf.extract("GIT_VERSION")
                        with open("GIT_VERSION") as gv:
                            logger.info(f"${artifact} git version: {gv.read()}")
                    except KeyError as e:
                        logger.warn(
                            f"Failed to get version from {artifact_path}: {str(e)}",
                            exc_info=True,
                        )
                    except Exception as e:
                        raise Exception(
                            "Failed to print version from {artifact_path}"
                        ) from e

        artifact_copy_info = []
        if self.rdc and self.branch != self.git_default_branch:
            artifact_copy_info.append(
                (
                    self.rdc,
                    os.path.join(DATA_CATALOG_ROOT, self.rdc),
                    os.path.join(self.dataset_artifacts_path, self.rdc),
                )
            )

        if self.git_config_file_path and self.config_file_url:
            # TODO: add labels
            artifact_copy_info.append(
                (
                    "config.yaml",
                    os.path.join(
                        self.git_config_file_path,
                        self.client,
                        self.dataset,
                        self.revision,
                        "config.yaml",
                    ),
                    self.config_file_url,
                )
            )

        if artifact_copy_info:
            self.copy_artifacts_from_git_to_s3(artifact_copy_info)

        # parse coding_systems.csv and add pull latest ndc mappings file from git
        self.download_ndc_lookup_file()

    def download_ndc_lookup_file(self):
        def download_single_file_from_metadata_store_to_s3(src_path, dest_path):
            artifact_path, artifact_name = os.path.split(src_path)

            if not artifact_path.startswith("metaDataStore"):
                artifact_path = os.path.join("metaDataStore", artifact_path)

            logger.info(
                f"loeading {artifact_name} from metadata folder {artifact_path} to {dest_path}"
            )
            with tempfile.TemporaryDirectory() as tmp_git:
                with GitHook(self.git_meta_repo, self.private_key) as git:
                    git.clone(
                        target_dir=tmp_git,
                        branch=self.git_default_branch,
                        sparse_folders=[artifact_path],
                    )

                logger.info(f"Publish {artifact_name} to {dest_path}")

                artifact_dir = os.path.join(tmp_git, artifact_path, artifact_name)
                if not os.path.exists(artifact_dir):
                    err = f"Cannot find {artifact_dir}"
                    self.log.info(err)
                    raise AirflowException(err)
                else:
                    self.fs.put(artifact_dir, dest_path)

        if self.git_meta_repo is not None:
            coding_systems_path = os.path.join(
                self.dataset_artifacts_path, CODING_SYSTEMS_ARTIFACT
            )
            logger.info(f"coding system path: {coding_systems_path}")

            download_single_file_from_metadata_store_to_s3(
                self.load_ndc_mapping_file_name(coding_systems_path, self.fs),
                os.path.join(self.dataset_artifacts_path, NDC_CODING_SYSTEM_DEST_FILE),
            )
        else:
            logger.info(
                "skipping ndc artifacts (metadatastore repo is not configured) ..."
            )

    @staticmethod
    def load_ndc_mapping_file_name(coding_systems_file_path, fs):
        coding_system_record = namedtuple(
            "coding_system_record",
            "name, type, csResource, csInstanceResource, "
            "csInstanceDataResource, caseSensitive, "
            "codeProcessor",
        )

        with fs.open(coding_systems_file_path, "r") as f:
            reader = csv.reader(f)
            next(reader)
            for record in map(coding_system_record._make, reader):
                if record.name == NDC_CODING_SYSTEM:
                    logger.info(
                        f"found coding systems for {NDC_CODING_SYSTEM} in {record.csInstanceDataResource}"
                    )
                    return record.csInstanceDataResource

        raise Exception(f"coding system for {NDC_CODING_SYSTEM} not found")

    def check_revision(self):
        # validate revision
        if not self.revision.isnumeric():
            err = "Revision must be numeric. If running a fix, please use 01, 02, etc."
            self.log.info(err)
            raise AirflowException(err)

    def copy_artifacts_from_git_to_s3(self, artifact_copy_info):
        with tempfile.TemporaryDirectory() as tmp_git:
            with GitHook(self.repo, self.private_key) as git:
                git.clone(tmp_git, self.branch)

            for artifact_name, artifact_relative_path, put_path in artifact_copy_info:
                logger.info(f"Publish {artifact_name} to {self.dataset_artifacts_path}")

                artifact_dir = os.path.join(tmp_git, artifact_relative_path)
                if not os.path.exists(artifact_dir):
                    err = f"Cannot find {artifact_name}"
                    self.log.info(err)
                    raise AirflowException(err)
                else:
                    self.fs.put(artifact_dir, put_path)
